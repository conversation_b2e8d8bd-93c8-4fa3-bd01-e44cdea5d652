import { PublicClientApplication, LogLevel } from '@azure/msal-browser'

// Validate required environment variables for authentication
const requiredEnvVars = [
  'VUE_APP_CLIENT_ID',
  'VUE_APP_CLIENT_SECRET',
  'VUE_APP_API_AUTHORITY',
  'VUE_APP_API_SCOPES'
];
requiredEnvVars.forEach((varName) => {
  if (!process.env[varName]) {
    throw new Error(`Missing required environment variable: ${varName}`);
  }
});

// Parse scopes from environment variables
const parseScopes = (scopeString) => {
  return scopeString ? scopeString.split(',').map(scope => scope.trim()) : []
}

const apiScopes = parseScopes(process.env.VUE_APP_API_SCOPES)
const graphScopes = parseScopes(process.env.VUE_APP_GRAPH_SCOPES)

// MSAL Configuration
const msalConfig = {
  auth: {
    clientId: process.env.VUE_APP_CLIENT_ID, // Replace with your Entra ID app registration client ID
    clientSecret: process.env.VUE_APP_CLIENT_SECRET, // Replace with your Entra ID app registration client secret
    authority: process.env.VUE_APP_API_AUTHORITY, // Replace with your tenant ID
    redirectUri: window.location.origin,
    postLogoutRedirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        switch (level) {
          case LogLevel.Error:
            console.error(message)
            return
          case LogLevel.Info:
            console.info(message)
            return
          case LogLevel.Verbose:
            console.debug(message)
            return
          case LogLevel.Warning:
            console.warn(message)
            return
        }
      },
    },
  },
}

// Request configuration for login - include both API and Graph scopes
const loginRequest = {
  scopes: [...apiScopes, ...graphScopes], // Include all scopes for login
}

// Request configurations for different token types
const apiTokenRequest = {
  scopes: apiScopes,
  account: null,
}

const graphTokenRequest = {
  scopes: graphScopes,
  account: null,
}

class AuthService {
  constructor() {
    this.msalInstance = null
    this.account = null
    this.stateChangeCallbacks = []
    this.initialized = false
    this.initializationPromise = null
  }

  // Add callback for state changes
  onStateChange(callback) {
    this.stateChangeCallbacks.push(callback)
    // If already initialized, call callback immediately with current state
    if (this.initialized) {
      callback(this.account)
    }
  }

  // Notify all callbacks of state change
  notifyStateChange() {
    this.stateChangeCallbacks.forEach(callback => callback(this.account))
  }

  // Auto-initialize when needed
  async ensureInitialized() {
    if (this.initialized) {
      return
    }

    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this.initialize()
    return this.initializationPromise
  }

  async initialize() {
    if (this.initialized) {
      return
    }

    try {
      this.msalInstance = new PublicClientApplication(msalConfig)
      await this.msalInstance.initialize()

      // Handle redirect promise
      const response = await this.msalInstance.handleRedirectPromise()
      if (response) {
        this.account = response.account
        this.notifyStateChange()
      } else {
        // Check if user is already logged in
        const accounts = this.msalInstance.getAllAccounts()
        if (accounts.length > 0) {
          this.account = accounts[0]
          this.notifyStateChange()
        }
      }

      this.initialized = true
    } catch (error) {
      console.error('MSAL initialization failed:', error)
      this.initializationPromise = null
      throw error
    }
  }

  async login() {
    await this.ensureInitialized()
    try {
      const response = await this.msalInstance.loginPopup(loginRequest)
      this.account = response.account
      this.notifyStateChange()
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  async logout() {
    await this.ensureInitialized()
    try {
      await this.msalInstance.logoutPopup({
        account: this.account,
      })
      this.account = null
      this.notifyStateChange()
    } catch (error) {
      console.error('Logout failed:', error)
      throw error
    }
  }

  async getAccessToken(scopeType = 'api') {
    await this.ensureInitialized()
    if (!this.account) {
      throw new Error('No account available')
    }

    // Choose the appropriate token request based on scope type
    const tokenRequest = scopeType === 'graph' ? { ...graphTokenRequest } : { ...apiTokenRequest }
    tokenRequest.account = this.account

    try {
      // Try to get token silently first
      const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
      return { accessToken: response.accessToken, correlationId: response.correlationId }
    } catch (error) {
      console.warn('Silent token acquisition failed, trying popup:', error)

      // If silent acquisition fails, try popup
      try {
        const response = await this.msalInstance.acquireTokenPopup(tokenRequest)
        return { accessToken: response.accessToken, correlationId: response.correlationId }
      } catch (popupError) {
        console.error('Token acquisition failed:', popupError)
        throw popupError
      }
    }
  }

  // Get token specifically for API calls
  async getApiToken() {
    return this.getAccessToken('api')
  }

  // Get token specifically for Graph API calls
  async getGraphToken() {
    return this.getAccessToken('graph')
  }

  isAuthenticated() {
    // Auto-initialize if not done yet, but don't wait for it
    if (!this.initialized && !this.initializationPromise) {
      this.ensureInitialized().catch(console.error)
    }
    return this.account !== null
  }

  getAccount() {
    return this.account
  }

  getUserInfo() {
    if (!this.account) return null
    
    return {
      id: this.account.homeAccountId,
      username: this.account.idTokenClaims.preferred_username,
      name: this.account.idTokenClaims.name,
      email: this.account.idTokenClaims.email,
    }
  }
}

export const authService = new AuthService()