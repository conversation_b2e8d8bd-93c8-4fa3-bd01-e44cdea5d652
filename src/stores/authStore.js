import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/authService'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Initialize auth service state change listener
  authService.onStateChange((account) => {
    if (account) {
      user.value = authService.getUserInfo()
    } else {
      user.value = null
    }
  })

  // Getters
  const isAuthenticated = computed(() => {
    return authService.isAuthenticated()
  })

  const userInfo = computed(() => {
    return authService.getUserInfo()
  })

  const login = async () => {
    loading.value = true
    error.value = null

    try {
      await authService.login()
      // State will be updated via the onStateChange callback
    } catch (err) {
      error.value = err.message || 'Login failed'
      console.error('<PERSON><PERSON> failed:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    error.value = null

    try {
      await authService.logout()
      // State will be updated via the onStateChange callback
    } catch (err) {
      error.value = err.message || 'Logout failed'
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const getAccessToken = async (scopeType = 'api') => {
    try {
      return await authService.getAccessToken(scopeType)
    } catch (err) {
      error.value = err.message || 'Failed to get access token'
      throw err
    }
  }

  const getApiToken = async () => {
    return getAccessToken('api')
  }

  const getGraphToken = async () => {
    return getAccessToken('graph')
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,

    // Getters
    isAuthenticated,
    userInfo,

    // Actions
    login,
    logout,
    getAccessToken,
    getApiToken,
    getGraphToken,
    clearError,
  }
})