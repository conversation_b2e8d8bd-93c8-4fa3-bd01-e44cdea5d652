import { createRouter, createWebHistory } from 'vue-router' 
import { useAuthStore } from '../stores/authStore'

const routes = [
  {
    path: '/',
    component: () => import("../layouts/default/DefaultLayout.vue"),
    children: [
      {
        path: "",
        name: 'AccommodationIndex',
        component: () => import("../views/AccommodationIndex.vue"),
        meta: { requiresAuth: true },
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import("../views/LoginView.vue"),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})


// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Wait a bit for auth state to be ready after login
  await new Promise(resolve => setTimeout(resolve, 100))

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isAuthenticated = authStore.isAuthenticated

  console.log('Router guard:', {
    to: to.path,
    from: from.path,
    requiresAuth,
    isAuthenticated
  })

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login if route requires auth and user is not authenticated
    console.log('Redirecting to login - auth required but not authenticated')
    next('/login')
  } else if (to.name === 'Login' && isAuthenticated) {
    // Redirect to home if user is already authenticated and tries to access login
    console.log('Redirecting to home - already authenticated')
    next('/')
  } else {
    console.log('Allowing navigation')
    next()
  }
})

export default router